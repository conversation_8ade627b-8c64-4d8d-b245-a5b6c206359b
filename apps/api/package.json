{"name": "api", "scripts": {"dev": "wrangler dev --port 9921", "test": "vitest", "db:generate": "drizzle-kit generate", "db:migrate": "tsx ./src/database/migrate.ts"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.61", "@cloudflare/workers-types": "^4.20250807.0", "@muraadso/tsconfig": "workspace:*", "@types/bcryptjs": "^3.0.0", "@types/node": "^24.2.0", "@types/react": "^19", "@vitest/coverage-v8": "catalog:", "dotenv": "^17.2.1", "drizzle-kit": "^0.31.4", "tsx": "^4.20.3", "typescript": "catalog:", "vitest": "catalog:", "wrangler": "catalog:"}, "dependencies": {"@hono/zod-validator": "^0.7.2", "@muraadso/types": "workspace:*", "bcryptjs": "^3.0.2", "drizzle-orm": "^0.44.4", "hono": "^4.8.12", "postgres": "^3.4.7", "uuid": "^11.1.0", "zod": "catalog:"}}